<?xml version="1.0" encoding="utf-8"?>
<network>
	<info>
		<version>1</version>
		<name>torch-jit-export</name>
		<from>InferenceEngine-v11</from>
		<when>2025.03.15 18:50:16</when>
		<synet>@VERSION@</synet>
	</info>
	<layers>
		<item>
			<type>Input</type>
			<name>image</name>
			<dst>image</dst>
			<input>
				<shape>
					<item>
						<dim>1 384 384 3</dim>
						<format>Nhwc</format>
					</item>
				</shape>
			</input>
		</item>
		<item>
			<type>Scale</type>
			<name>Divide_2410</name>
			<src>image</src>
			<dst>image</dst>
			<weight>
				<item>
					<dim>3</dim>
					<offset>0</offset>
					<size>12</size>
				</item>
			</weight>
		</item>
		<item>
			<type>Convolution</type>
			<name>Conv_0</name>
			<src>image</src>
			<dst>Conv_0</dst>
			<weight>
				<item>
					<dim>3 3 3 32</dim>
					<format>Nhwc</format>
					<offset>12</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>3468</offset>
					<size>128</size>
				</item>
			</weight>
			<convolution>
				<outputNum>32</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<activationType>RestrictRange</activationType>
			</convolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_12</name>
			<src>Conv_0</src>
			<dst>Conv_12</dst>
			<weight>
				<item>
					<dim>1 1 32 32</dim>
					<format>Nhwc</format>
					<offset>3596</offset>
					<size>4096</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>7692</offset>
					<size>128</size>
				</item>
				<item>
					<dim>3 3 1 32</dim>
					<format>Nhwc</format>
					<offset>7820</offset>
					<size>1152</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>8972</offset>
					<size>128</size>
				</item>
				<item>
					<dim>1 1 32 16</dim>
					<format>Nhwc</format>
					<offset>9100</offset>
					<size>2048</size>
				</item>
				<item>
					<dim>16</dim>
					<offset>11148</offset>
					<size>64</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>32</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>16</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_21</name>
			<src>Conv_12</src>
			<dst>Conv_21</dst>
			<weight>
				<item>
					<dim>1 1 16 96</dim>
					<format>Nhwc</format>
					<offset>11212</offset>
					<size>6144</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>17356</offset>
					<size>384</size>
				</item>
				<item>
					<dim>3 3 1 96</dim>
					<format>Nhwc</format>
					<offset>17740</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>21196</offset>
					<size>384</size>
				</item>
				<item>
					<dim>1 1 96 24</dim>
					<format>Nhwc</format>
					<offset>21580</offset>
					<size>9216</size>
				</item>
				<item>
					<dim>24</dim>
					<offset>30796</offset>
					<size>96</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>96</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>24</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_31</name>
			<src>Conv_21</src>
			<dst>Add_31</dst>
			<weight>
				<item>
					<dim>1 1 24 144</dim>
					<format>Nhwc</format>
					<offset>30892</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>44716</offset>
					<size>576</size>
				</item>
				<item>
					<dim>3 3 1 144</dim>
					<format>Nhwc</format>
					<offset>45292</offset>
					<size>5184</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>50476</offset>
					<size>576</size>
				</item>
				<item>
					<dim>1 1 144 24</dim>
					<format>Nhwc</format>
					<offset>51052</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>24</dim>
					<offset>64876</offset>
					<size>96</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>144</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>144</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>144</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>24</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_40</name>
			<src>Add_31</src>
			<dst>Conv_40</dst>
			<weight>
				<item>
					<dim>1 1 24 144</dim>
					<format>Nhwc</format>
					<offset>64972</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>78796</offset>
					<size>576</size>
				</item>
				<item>
					<dim>3 3 1 144</dim>
					<format>Nhwc</format>
					<offset>79372</offset>
					<size>5184</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>84556</offset>
					<size>576</size>
				</item>
				<item>
					<dim>1 1 144 32</dim>
					<format>Nhwc</format>
					<offset>85132</offset>
					<size>18432</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>103564</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>144</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>144</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>144</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_50</name>
			<src>Conv_40</src>
			<dst>Add_50</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>103692</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>128268</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>129036</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>135948</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 32</dim>
					<format>Nhwc</format>
					<offset>136716</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>161292</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_60</name>
			<src>Add_50</src>
			<dst>Add_60</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>161420</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>185996</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>186764</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>193676</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 32</dim>
					<format>Nhwc</format>
					<offset>194444</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>219020</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_69</name>
			<src>Add_60</src>
			<dst>Conv_69</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>219148</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>243724</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>244492</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>251404</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 64</dim>
					<format>Nhwc</format>
					<offset>252172</offset>
					<size>49152</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>301324</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_79</name>
			<src>Conv_69</src>
			<dst>Add_79</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>301580</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>399884</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>401420</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>415244</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>416780</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>515084</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_89</name>
			<src>Add_79</src>
			<dst>Add_89</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>515340</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>613644</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>615180</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>629004</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>630540</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>728844</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_99</name>
			<src>Add_89</src>
			<dst>Add_99</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>729100</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>827404</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>828940</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>842764</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>844300</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>942604</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_108</name>
			<src>Add_99</src>
			<dst>Conv_108</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>942860</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>1041164</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>1042700</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>1056524</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 96</dim>
					<format>Nhwc</format>
					<offset>1058060</offset>
					<size>147456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>1205516</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_118</name>
			<src>Conv_108</src>
			<dst>Add_118</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>1205900</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1427084</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>1429388</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1450124</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 96</dim>
					<format>Nhwc</format>
					<offset>1452428</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>1673612</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_128</name>
			<src>Add_118</src>
			<dst>Add_128</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>1673996</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1895180</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>1897484</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1918220</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 96</dim>
					<format>Nhwc</format>
					<offset>1920524</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>2141708</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_172</name>
			<src>Add_128</src>
			<dst>Conv_172</dst>
			<weight>
				<item>
					<dim>3 3 1 96</dim>
					<format>Nhwc</format>
					<offset>2142092</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>2145548</offset>
					<size>384</size>
				</item>
				<item>
					<dim>1 1 96 16</dim>
					<format>Nhwc</format>
					<offset>2145932</offset>
					<size>6144</size>
				</item>
				<item>
					<dim>16</dim>
					<offset>2152076</offset>
					<size>64</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>96</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>96</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>16</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_195</name>
			<src>Conv_172</src>
			<dst>Transpose_195</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_196</name>
			<src>Conv_172</src>
			<dst>Shape_196</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_6363</name>
			<dst>Constant_6363</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_198</name>
			<src>Shape_196 Constant_6363</src>
			<dst>Gather_198</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>896</name>
			<dst>896</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>897</name>
			<dst>897</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_200</name>
			<src>Gather_198 896 897</src>
			<dst>Concat_200</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_201</name>
			<src>Transpose_195 Concat_200</src>
			<dst>Reshape_201</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_217</name>
			<src>Reshape_201</src>
			<dst>Reshape_217</dst>
			<reshape>
				<shape>9216</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_137</name>
			<src>Add_128</src>
			<dst>Conv_137</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>2152212</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>2373396</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>2375700</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>2396436</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 160</dim>
					<format>Nhwc</format>
					<offset>2398740</offset>
					<size>368640</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>2767380</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_147</name>
			<src>Conv_137</src>
			<dst>Add_147</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>2768020</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>3382420</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>3386260</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>3420820</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 160</dim>
					<format>Nhwc</format>
					<offset>3424660</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>4039060</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_157</name>
			<src>Add_147</src>
			<dst>Add_157</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>4039700</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>4654100</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>4657940</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>4692500</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 160</dim>
					<format>Nhwc</format>
					<offset>4696340</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>5310740</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_166</name>
			<src>Add_157</src>
			<dst>Conv_166</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>5311380</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>5925780</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>5929620</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>5964180</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 320</dim>
					<format>Nhwc</format>
					<offset>5968020</offset>
					<size>1228800</size>
				</item>
				<item>
					<dim>320</dim>
					<offset>7196820</offset>
					<size>1280</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>320</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_178</name>
			<src>Conv_166</src>
			<dst>Conv_178</dst>
			<weight>
				<item>
					<dim>3 3 1 320</dim>
					<format>Nhwc</format>
					<offset>7198100</offset>
					<size>11520</size>
				</item>
				<item>
					<dim>320</dim>
					<offset>7209620</offset>
					<size>1280</size>
				</item>
				<item>
					<dim>1 1 320 20</dim>
					<format>Nhwc</format>
					<offset>7210900</offset>
					<size>25600</size>
				</item>
				<item>
					<dim>20</dim>
					<offset>7236500</offset>
					<size>80</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>320</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>320</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>20</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_242</name>
			<src>Conv_178</src>
			<dst>Transpose_242</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_243</name>
			<src>Conv_178</src>
			<dst>Shape_243</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_6373</name>
			<dst>Constant_6373</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_245</name>
			<src>Shape_243 Constant_6373</src>
			<dst>Gather_245</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>904</name>
			<dst>904</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>905</name>
			<dst>905</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_247</name>
			<src>Gather_245 904 905</src>
			<dst>Concat_247</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_248</name>
			<src>Transpose_242 Concat_247</src>
			<dst>Reshape_248</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_264</name>
			<src>Reshape_248</src>
			<dst>Reshape_264</dst>
			<reshape>
				<shape>2880</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Concat</type>
			<name>Concat_282</name>
			<src>Reshape_217 Reshape_264</src>
			<dst>Concat_282</dst>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_169</name>
			<src>Add_128</src>
			<dst>Conv_169</dst>
			<weight>
				<item>
					<dim>3 3 1 96</dim>
					<format>Nhwc</format>
					<offset>7236580</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>7240036</offset>
					<size>384</size>
				</item>
				<item>
					<dim>1 1 96 8</dim>
					<format>Nhwc</format>
					<offset>7240420</offset>
					<size>3072</size>
				</item>
				<item>
					<dim>8</dim>
					<offset>7243492</offset>
					<size>32</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>96</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>96</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>8</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_181</name>
			<src>Conv_169</src>
			<dst>Transpose_181</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_182</name>
			<src>Conv_169</src>
			<dst>Shape_182</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_6383</name>
			<dst>Constant_6383</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_184</name>
			<src>Shape_182 Constant_6383</src>
			<dst>Gather_184</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>893</name>
			<dst>893</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_186</name>
			<src>Gather_184 893</src>
			<dst>Concat_186</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_187</name>
			<src>Transpose_181 Concat_186</src>
			<dst>Reshape_187</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_193</name>
			<src>Reshape_187</src>
			<dst>Reshape_193</dst>
			<reshape>
				<shape>2304 2</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Softmax</type>
			<name>Softmax_194</name>
			<src>Reshape_193</src>
			<dst>Reshape_193</dst>
			<softmax>
				<axis>2</axis>
			</softmax>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_211</name>
			<src>Reshape_193</src>
			<dst>Reshape_211</dst>
			<reshape>
				<shape>2304 2</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_175</name>
			<src>Conv_166</src>
			<dst>Conv_175</dst>
			<weight>
				<item>
					<dim>3 3 1 320</dim>
					<format>Nhwc</format>
					<offset>7243572</offset>
					<size>11520</size>
				</item>
				<item>
					<dim>320</dim>
					<offset>7255092</offset>
					<size>1280</size>
				</item>
				<item>
					<dim>1 1 320 10</dim>
					<format>Nhwc</format>
					<offset>7256372</offset>
					<size>12800</size>
				</item>
				<item>
					<dim>10</dim>
					<offset>7269172</offset>
					<size>40</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>320</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>320</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>10</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_228</name>
			<src>Conv_175</src>
			<dst>Transpose_228</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_229</name>
			<src>Conv_175</src>
			<dst>Shape_229</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_6403</name>
			<dst>Constant_6403</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_231</name>
			<src>Shape_229 Constant_6403</src>
			<dst>Gather_231</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>901</name>
			<dst>901</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_233</name>
			<src>Gather_231 901</src>
			<dst>Concat_233</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_234</name>
			<src>Transpose_228 Concat_233</src>
			<dst>Reshape_234</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_240</name>
			<src>Reshape_234</src>
			<dst>Reshape_240</dst>
			<reshape>
				<shape>720 2</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Softmax</type>
			<name>Softmax_241</name>
			<src>Reshape_240</src>
			<dst>Reshape_240</dst>
			<softmax>
				<axis>2</axis>
			</softmax>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_258</name>
			<src>Reshape_240</src>
			<dst>Reshape_258</dst>
			<reshape>
				<shape>720 2</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Concat</type>
			<name>Concat_275</name>
			<src>Reshape_211 Reshape_258</src>
			<dst>Concat_275</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_281</name>
			<src>Concat_275</src>
			<dst>Reshape_281</dst>
			<reshape>
				<shape>6048</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_2218</name>
			<src>Add_128</src>
			<dst>ShapeOf_2218</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2221</name>
			<dst>Constant_2221</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2220</name>
			<dst>Constant_2220</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2222</name>
			<dst>Constant_2222</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_2223</name>
			<src>ShapeOf_2218 Constant_2221 Constant_2220 Constant_2222</src>
			<dst>StridedSlice_2223</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_2219</name>
			<src>image</src>
			<dst>ShapeOf_2219</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2225</name>
			<dst>Constant_2225</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2224</name>
			<dst>Constant_2224</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2226</name>
			<dst>Constant_2226</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_2227</name>
			<src>ShapeOf_2219 Constant_2225 Constant_2224 Constant_2226</src>
			<dst>StridedSlice_2227</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>PriorBoxClustered</type>
			<name>PriorBoxClustered_2229</name>
			<src>StridedSlice_2223 StridedSlice_2227</src>
			<dst>PriorBoxClustered_2229</dst>
			<priorBoxClustered>
				<widths>10.746300 19.509899 22.972900 35.358601</widths>
				<heights>18.429399 43.797600 80.250298 128.087997</heights>
				<variance>0.100000 0.100000 0.200000 0.200000</variance>
				<step>16.000000</step>
			</priorBoxClustered>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_227</name>
			<src>PriorBoxClustered_2229</src>
			<dst>Reshape_227</dst>
			<reshape>
				<shape>2 9216</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_2233</name>
			<src>Conv_166</src>
			<dst>ShapeOf_2233</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2236</name>
			<dst>Constant_2236</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2235</name>
			<dst>Constant_2235</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2237</name>
			<dst>Constant_2237</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_2238</name>
			<src>ShapeOf_2233 Constant_2236 Constant_2235 Constant_2237</src>
			<dst>StridedSlice_2238</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2240</name>
			<dst>Constant_2240</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2239</name>
			<dst>Constant_2239</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_2241</name>
			<dst>Constant_2241</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_2242</name>
			<src>ShapeOf_2219 Constant_2240 Constant_2239 Constant_2241</src>
			<dst>StridedSlice_2242</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>PriorBoxClustered</type>
			<name>PriorBoxClustered_2244</name>
			<src>StridedSlice_2238 StridedSlice_2242</src>
			<dst>PriorBoxClustered_2244</dst>
			<priorBoxClustered>
				<widths>77.796700 48.891499 74.294998 137.432999 225.498993</widths>
				<heights>64.304497 189.787994 285.380005 157.679993 219.643997</heights>
				<variance>0.100000 0.100000 0.200000 0.200000</variance>
				<step>32.000000</step>
			</priorBoxClustered>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_274</name>
			<src>PriorBoxClustered_2244</src>
			<dst>Reshape_274</dst>
			<reshape>
				<shape>2 2880</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Concat</type>
			<name>Concat_283</name>
			<src>Reshape_227 Reshape_274</src>
			<dst>Concat_283</dst>
			<concat>
				<axis>2</axis>
			</concat>
		</item>
		<item>
			<type>DetectionOutput</type>
			<name>detection_out</name>
			<src>Concat_282 Reshape_281 Concat_283</src>
			<dst>detection_out</dst>
			<detectionOutput>
				<backgroundLabelId>1</backgroundLabelId>
				<nms>
					<nmsThreshold>0.750000</nmsThreshold>
					<topK>200</topK>
				</nms>
				<codeType>CenterSize</codeType>
				<keepTopK>200</keepTopK>
				<confidenceThreshold>0.050000</confidenceThreshold>
			</detectionOutput>
		</item>
	</layers>
</network>

