<?xml version="1.0" encoding="utf-8"?>
<network>
	<info>
		<version>1</version>
		<name>torch-jit-export</name>
		<from>InferenceEngine-v11</from>
		<when>2023.05.08 17:41:43</when>
		<synet>@VERSION@</synet>
	</info>
	<layers>
		<item>
			<type>Input</type>
			<name>image</name>
			<dst>image</dst>
			<input>
				<shape>
					<item>
						<dim>1 256 256 3</dim>
						<format>Nhwc</format>
					</item>
				</shape>
			</input>
		</item>
		<item>
			<type>Power</type>
			<name>Divide_1793</name>
			<src>image</src>
			<dst>image</dst>
			<power>
				<scale>0.003922</scale>
			</power>
		</item>
		<item>
			<type>Convolution</type>
			<name>BatchNormalization_1</name>
			<src>image</src>
			<dst>BatchNormalization_1</dst>
			<weight>
				<item>
					<dim>3 3 3 32</dim>
					<format>Nhwc</format>
					<offset>4</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>3460</offset>
					<size>128</size>
				</item>
			</weight>
			<convolution>
				<outputNum>32</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<activationType>RestrictRange</activationType>
			</convolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>BatchNormalization_10</name>
			<src>BatchNormalization_1</src>
			<dst>BatchNormalization_10</dst>
			<weight>
				<item>
					<dim>1 1 32 32</dim>
					<format>Nhwc</format>
					<offset>3588</offset>
					<size>4096</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>7684</offset>
					<size>128</size>
				</item>
				<item>
					<dim>3 3 1 32</dim>
					<format>Nhwc</format>
					<offset>7812</offset>
					<size>1152</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>8964</offset>
					<size>128</size>
				</item>
				<item>
					<dim>1 1 32 16</dim>
					<format>Nhwc</format>
					<offset>9092</offset>
					<size>2048</size>
				</item>
				<item>
					<dim>16</dim>
					<offset>11140</offset>
					<size>64</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>32</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>16</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>BatchNormalization_18</name>
			<src>BatchNormalization_10</src>
			<dst>BatchNormalization_18</dst>
			<weight>
				<item>
					<dim>1 1 16 96</dim>
					<format>Nhwc</format>
					<offset>11204</offset>
					<size>6144</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>17348</offset>
					<size>384</size>
				</item>
				<item>
					<dim>3 3 1 96</dim>
					<format>Nhwc</format>
					<offset>17732</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>21188</offset>
					<size>384</size>
				</item>
				<item>
					<dim>1 1 96 24</dim>
					<format>Nhwc</format>
					<offset>21572</offset>
					<size>9216</size>
				</item>
				<item>
					<dim>24</dim>
					<offset>30788</offset>
					<size>96</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>96</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>24</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_27</name>
			<src>BatchNormalization_18</src>
			<dst>Add_27</dst>
			<weight>
				<item>
					<dim>1 1 24 144</dim>
					<format>Nhwc</format>
					<offset>30884</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>44708</offset>
					<size>576</size>
				</item>
				<item>
					<dim>3 3 1 144</dim>
					<format>Nhwc</format>
					<offset>45284</offset>
					<size>5184</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>50468</offset>
					<size>576</size>
				</item>
				<item>
					<dim>1 1 144 24</dim>
					<format>Nhwc</format>
					<offset>51044</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>24</dim>
					<offset>64868</offset>
					<size>96</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>144</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>144</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>144</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>24</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>BatchNormalization_35</name>
			<src>Add_27</src>
			<dst>BatchNormalization_35</dst>
			<weight>
				<item>
					<dim>1 1 24 144</dim>
					<format>Nhwc</format>
					<offset>64964</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>78788</offset>
					<size>576</size>
				</item>
				<item>
					<dim>3 3 1 144</dim>
					<format>Nhwc</format>
					<offset>79364</offset>
					<size>5184</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>84548</offset>
					<size>576</size>
				</item>
				<item>
					<dim>1 1 144 32</dim>
					<format>Nhwc</format>
					<offset>85124</offset>
					<size>18432</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>103556</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>144</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>144</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>144</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_44</name>
			<src>BatchNormalization_35</src>
			<dst>Add_44</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>103684</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>128260</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>129028</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>135940</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 32</dim>
					<format>Nhwc</format>
					<offset>136708</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>161284</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_53</name>
			<src>Add_44</src>
			<dst>Add_53</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>161412</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>185988</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>186756</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>193668</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 32</dim>
					<format>Nhwc</format>
					<offset>194436</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>219012</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>BatchNormalization_61</name>
			<src>Add_53</src>
			<dst>BatchNormalization_61</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>219140</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>243716</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>244484</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>251396</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 64</dim>
					<format>Nhwc</format>
					<offset>252164</offset>
					<size>49152</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>301316</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_70</name>
			<src>BatchNormalization_61</src>
			<dst>Add_70</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>301572</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>399876</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>401412</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>415236</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>416772</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>515076</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_79</name>
			<src>Add_70</src>
			<dst>Add_79</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>515332</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>613636</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>615172</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>628996</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>630532</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>728836</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_88</name>
			<src>Add_79</src>
			<dst>Add_88</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>729092</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>827396</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>828932</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>842756</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>844292</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>942596</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>BatchNormalization_96</name>
			<src>Add_88</src>
			<dst>BatchNormalization_96</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>942852</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>1041156</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>1042692</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>1056516</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 96</dim>
					<format>Nhwc</format>
					<offset>1058052</offset>
					<size>147456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>1205508</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_105</name>
			<src>BatchNormalization_96</src>
			<dst>Add_105</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>1205892</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1427076</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>1429380</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1450116</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 96</dim>
					<format>Nhwc</format>
					<offset>1452420</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>1673604</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_114</name>
			<src>Add_105</src>
			<dst>Add_114</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>1673988</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1895172</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>1897476</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1918212</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 96</dim>
					<format>Nhwc</format>
					<offset>1920516</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>2141700</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_156</name>
			<src>Add_114</src>
			<dst>Conv_156</dst>
			<weight>
				<item>
					<dim>3 3 1 96</dim>
					<format>Nhwc</format>
					<offset>2142084</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>2145540</offset>
					<size>384</size>
				</item>
				<item>
					<dim>1 1 96 16</dim>
					<format>Nhwc</format>
					<offset>2145924</offset>
					<size>6144</size>
				</item>
				<item>
					<dim>16</dim>
					<offset>2152068</offset>
					<size>64</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>96</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>96</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>16</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_196</name>
			<src>Conv_156</src>
			<dst>Transpose_196</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_197</name>
			<src>Conv_156</src>
			<dst>Shape_197</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_4924</name>
			<dst>Constant_4924</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_199</name>
			<src>Shape_197 Constant_4924</src>
			<dst>Gather_199</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>581</name>
			<dst>581</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_201</name>
			<src>Gather_199 581</src>
			<dst>Concat_201</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_202</name>
			<src>Transpose_196 Concat_201</src>
			<dst>Reshape_202</dst>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>BatchNormalization_122</name>
			<src>Add_114</src>
			<dst>BatchNormalization_122</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>2152180</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>2373364</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>2375668</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>2396404</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 160</dim>
					<format>Nhwc</format>
					<offset>2398708</offset>
					<size>368640</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>2767348</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_131</name>
			<src>BatchNormalization_122</src>
			<dst>Add_131</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>2767988</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>3382388</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>3386228</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>3420788</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 160</dim>
					<format>Nhwc</format>
					<offset>3424628</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>4039028</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Add_140</name>
			<src>Add_131</src>
			<dst>Add_140</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>4039668</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>4654068</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>4657908</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>4692468</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 160</dim>
					<format>Nhwc</format>
					<offset>4696308</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>5310708</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>BatchNormalization_148</name>
			<src>Add_140</src>
			<dst>BatchNormalization_148</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>5311348</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>5925748</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>5929588</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>5964148</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 320</dim>
					<format>Nhwc</format>
					<offset>5967988</offset>
					<size>1228800</size>
				</item>
				<item>
					<dim>320</dim>
					<offset>7196788</offset>
					<size>1280</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>RestrictRange</activationType>
					</item>
					<item>
						<outputNum>320</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_164</name>
			<src>BatchNormalization_148</src>
			<dst>Conv_164</dst>
			<weight>
				<item>
					<dim>3 3 1 320</dim>
					<format>Nhwc</format>
					<offset>7198068</offset>
					<size>11520</size>
				</item>
				<item>
					<dim>320</dim>
					<offset>7209588</offset>
					<size>1280</size>
				</item>
				<item>
					<dim>1 1 320 20</dim>
					<format>Nhwc</format>
					<offset>7210868</offset>
					<size>25600</size>
				</item>
				<item>
					<dim>20</dim>
					<offset>7236468</offset>
					<size>80</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>320</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>320</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>20</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_203</name>
			<src>Conv_164</src>
			<dst>Transpose_203</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_204</name>
			<src>Conv_164</src>
			<dst>Shape_204</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_4930</name>
			<dst>Constant_4930</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_206</name>
			<src>Shape_204 Constant_4930</src>
			<dst>Gather_206</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>582</name>
			<dst>582</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_208</name>
			<src>Gather_206 582</src>
			<dst>Concat_208</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_209</name>
			<src>Transpose_203 Concat_208</src>
			<dst>Reshape_209</dst>
		</item>
		<item>
			<type>Concat</type>
			<name>Concat_210</name>
			<src>Reshape_202 Reshape_209</src>
			<dst>Concat_210</dst>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_152</name>
			<src>Add_114</src>
			<dst>Conv_152</dst>
			<weight>
				<item>
					<dim>3 3 1 96</dim>
					<format>Nhwc</format>
					<offset>7236548</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>7240004</offset>
					<size>384</size>
				</item>
				<item>
					<dim>1 1 96 16</dim>
					<format>Nhwc</format>
					<offset>7240388</offset>
					<size>6144</size>
				</item>
				<item>
					<dim>16</dim>
					<offset>7246532</offset>
					<size>64</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>96</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>96</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>16</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_168</name>
			<src>Conv_152</src>
			<dst>Transpose_168</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_169</name>
			<src>Conv_152</src>
			<dst>Shape_169</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_4936</name>
			<dst>Constant_4936</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_171</name>
			<src>Shape_169 Constant_4936</src>
			<dst>Gather_171</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>576</name>
			<dst>576</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_173</name>
			<src>Gather_171 576</src>
			<dst>Concat_173</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_174</name>
			<src>Transpose_168 Concat_173</src>
			<dst>Reshape_174</dst>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>Conv_160</name>
			<src>BatchNormalization_148</src>
			<dst>Conv_160</dst>
			<weight>
				<item>
					<dim>3 3 1 320</dim>
					<format>Nhwc</format>
					<offset>7246596</offset>
					<size>11520</size>
				</item>
				<item>
					<dim>320</dim>
					<offset>7258116</offset>
					<size>1280</size>
				</item>
				<item>
					<dim>1 1 320 20</dim>
					<format>Nhwc</format>
					<offset>7259396</offset>
					<size>25600</size>
				</item>
				<item>
					<dim>20</dim>
					<offset>7284996</offset>
					<size>80</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>320</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>320</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>20</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Permute</type>
			<name>Transpose_175</name>
			<src>Conv_160</src>
			<dst>Transpose_175</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Meta</type>
			<name>Shape_176</name>
			<src>Conv_160</src>
			<dst>Shape_176</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_4942</name>
			<dst>Constant_4942</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Gather_178</name>
			<src>Shape_176 Constant_4942</src>
			<dst>Gather_178</dst>
			<meta>
				<type>Gather</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>577</name>
			<dst>577</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_180</name>
			<src>Gather_178 577</src>
			<dst>Concat_180</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_181</name>
			<src>Transpose_175 Concat_180</src>
			<dst>Reshape_181</dst>
		</item>
		<item>
			<type>Concat</type>
			<name>Concat_182</name>
			<src>Reshape_174 Reshape_181</src>
			<dst>Concat_182</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_188</name>
			<src>Concat_182</src>
			<dst>Reshape_188</dst>
			<reshape>
				<shape>1344 4</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_1705</name>
			<src>Reshape_188</src>
			<dst>ShapeOf_1705</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1708</name>
			<dst>Constant_1708</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1707</name>
			<dst>Constant_1707</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1709</name>
			<dst>Constant_1709</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_1710</name>
			<src>ShapeOf_1705 Constant_1708 Constant_1707 Constant_1709</src>
			<dst>StridedSlice_1710</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1711</name>
			<dst>Constant_1711</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>0</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>ReduceProd_1712</name>
			<src>StridedSlice_1710 Constant_1711</src>
			<dst>ReduceProd_1712</dst>
			<meta>
				<type>ReduceProd</type>
				<alpha>
					<type>32i</type>
					<shape>1</shape>
					<i32>1</i32>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1713</name>
			<dst>Constant_1713</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>-1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Concat_1714</name>
			<src>ReduceProd_1712 Constant_1713</src>
			<dst>Concat_1714</dst>
			<meta>
				<type>Pack</type>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_1715</name>
			<src>Reshape_188 Concat_1714</src>
			<dst>Reshape_1715</dst>
		</item>
		<item>
			<type>Softmax</type>
			<name>Softmax_1746</name>
			<src>Reshape_1715</src>
			<dst>Reshape_1715</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Softmax_189</name>
			<src>Reshape_1715 ShapeOf_1705</src>
			<dst>Softmax_189</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_195</name>
			<src>Softmax_189</src>
			<dst>Reshape_195</dst>
			<reshape>
				<shape>5376</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_1644</name>
			<src>Add_114</src>
			<dst>ShapeOf_1644</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1647</name>
			<dst>Constant_1647</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1646</name>
			<dst>Constant_1646</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1648</name>
			<dst>Constant_1648</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_1649</name>
			<src>ShapeOf_1644 Constant_1647 Constant_1646 Constant_1648</src>
			<dst>StridedSlice_1649</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_1645</name>
			<src>image</src>
			<dst>ShapeOf_1645</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1651</name>
			<dst>Constant_1651</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1650</name>
			<dst>Constant_1650</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1652</name>
			<dst>Constant_1652</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_1653</name>
			<src>ShapeOf_1645 Constant_1651 Constant_1650 Constant_1652</src>
			<dst>StridedSlice_1653</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>PriorBoxClustered</type>
			<name>PriorBoxClustered_1655</name>
			<src>StridedSlice_1649 StridedSlice_1653</src>
			<dst>PriorBoxClustered_1655</dst>
			<priorBoxClustered>
				<widths>7.164180 13.006600 15.315300 23.572399</widths>
				<heights>12.286300 29.198400 53.500198 85.391701</heights>
				<variance>0.100000 0.100000 0.200000 0.200000</variance>
				<step>16.000000</step>
			</priorBoxClustered>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_1658</name>
			<src>BatchNormalization_148</src>
			<dst>ShapeOf_1658</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1661</name>
			<dst>Constant_1661</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1660</name>
			<dst>Constant_1660</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1662</name>
			<dst>Constant_1662</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_1663</name>
			<src>ShapeOf_1658 Constant_1661 Constant_1660 Constant_1662</src>
			<dst>StridedSlice_1663</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1665</name>
			<dst>Constant_1665</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>2</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1664</name>
			<dst>Constant_1664</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>4</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>Constant_1666</name>
			<dst>Constant_1666</dst>
			<meta>
				<type>Const</type>
				<alpha>
					<type>64i</type>
					<shape>1</shape>
					<i64>1</i64>
				</alpha>
			</meta>
		</item>
		<item>
			<type>Meta</type>
			<name>StridedSlice_1667</name>
			<src>ShapeOf_1645 Constant_1665 Constant_1664 Constant_1666</src>
			<dst>StridedSlice_1667</dst>
			<meta>
				<type>StridedSlice</type>
			</meta>
		</item>
		<item>
			<type>PriorBoxClustered</type>
			<name>PriorBoxClustered_1669</name>
			<src>StridedSlice_1663 StridedSlice_1667</src>
			<dst>PriorBoxClustered_1669</dst>
			<priorBoxClustered>
				<widths>51.864498 32.594299 49.529999 91.621902 150.332993</widths>
				<heights>42.869701 126.526001 190.253006 105.120003 146.429001</heights>
				<variance>0.100000 0.100000 0.200000 0.200000</variance>
				<step>32.000000</step>
			</priorBoxClustered>
		</item>
		<item>
			<type>Concat</type>
			<name>Concat_167</name>
			<src>PriorBoxClustered_1655 PriorBoxClustered_1669</src>
			<dst>Concat_167</dst>
			<concat>
				<axis>2</axis>
			</concat>
		</item>
		<item>
			<type>DetectionOutput</type>
			<name>detection_out</name>
			<src>Concat_210 Reshape_195 Concat_167</src>
			<dst>detection_out</dst>
			<detectionOutput>
				<backgroundLabelId>3</backgroundLabelId>
				<nms>
					<nmsThreshold>0.450000</nmsThreshold>
					<topK>200</topK>
				</nms>
				<codeType>CenterSize</codeType>
				<keepTopK>200</keepTopK>
				<confidenceThreshold>0.020000</confidenceThreshold>
			</detectionOutput>
		</item>
	</layers>
</network>

