<?xml version="1.0" encoding="utf-8"?>
<network>
	<info>
		<version>1</version>
		<name>torch-jit-export</name>
		<from>InferenceEngine-v11</from>
		<when>2023.05.08 17:46:41</when>
		<synet>@VERSION@</synet>
	</info>
	<layers>
		<item>
			<type>Input</type>
			<name>input</name>
			<dst>input</dst>
			<input>
				<shape>
					<item>
						<dim>1 72 72 3</dim>
						<format>Nhwc</format>
					</item>
				</shape>
			</input>
		</item>
		<item>
			<type>Bias</type>
			<name>Subtract_268</name>
			<src>input</src>
			<dst>Subtract_268</dst>
			<weight>
				<item>
					<dim>3</dim>
					<offset>0</offset>
					<size>12</size>
				</item>
			</weight>
		</item>
		<item>
			<type>Convolution</type>
			<name>128</name>
			<src>Subtract_268</src>
			<dst>128</dst>
			<weight>
				<item>
					<dim>7 7 3 64</dim>
					<format>Nhwc</format>
					<offset>12</offset>
					<size>37632</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>37644</offset>
					<size>256</size>
				</item>
			</weight>
			<convolution>
				<outputNum>64</outputNum>
				<kernel>7 7</kernel>
				<pad>3 3 3 3</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Pooling</type>
			<name>130</name>
			<src>128</src>
			<dst>130</dst>
			<pooling>
				<method>Max</method>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<roundingType>Floor</roundingType>
			</pooling>
		</item>
		<item>
			<type>Convolution</type>
			<name>132</name>
			<src>130</src>
			<dst>132</dst>
			<weight>
				<item>
					<dim>3 3 64 64</dim>
					<format>Nhwc</format>
					<offset>37900</offset>
					<size>147456</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>185356</offset>
					<size>256</size>
				</item>
			</weight>
			<convolution>
				<outputNum>64</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>135</name>
			<src>132</src>
			<dst>135</dst>
			<weight>
				<item>
					<dim>3 3 64 64</dim>
					<format>Nhwc</format>
					<offset>185612</offset>
					<size>147456</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>333068</offset>
					<size>256</size>
				</item>
			</weight>
			<convolution>
				<outputNum>64</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>136</name>
			<src>135 130</src>
			<dst>136</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>137</name>
			<src>136</src>
			<dst>136</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>139</name>
			<src>136</src>
			<dst>139</dst>
			<weight>
				<item>
					<dim>3 3 64 64</dim>
					<format>Nhwc</format>
					<offset>333324</offset>
					<size>147456</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>480780</offset>
					<size>256</size>
				</item>
			</weight>
			<convolution>
				<outputNum>64</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>142</name>
			<src>139</src>
			<dst>142</dst>
			<weight>
				<item>
					<dim>3 3 64 64</dim>
					<format>Nhwc</format>
					<offset>481036</offset>
					<size>147456</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>628492</offset>
					<size>256</size>
				</item>
			</weight>
			<convolution>
				<outputNum>64</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>143</name>
			<src>142 136</src>
			<dst>143</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>144</name>
			<src>143</src>
			<dst>143</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>146</name>
			<src>143</src>
			<dst>146</dst>
			<weight>
				<item>
					<dim>3 3 64 128</dim>
					<format>Nhwc</format>
					<offset>628748</offset>
					<size>294912</size>
				</item>
				<item>
					<dim>128</dim>
					<offset>923660</offset>
					<size>512</size>
				</item>
			</weight>
			<convolution>
				<outputNum>128</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>149</name>
			<src>146</src>
			<dst>149</dst>
			<weight>
				<item>
					<dim>3 3 128 128</dim>
					<format>Nhwc</format>
					<offset>924172</offset>
					<size>589824</size>
				</item>
				<item>
					<dim>128</dim>
					<offset>1513996</offset>
					<size>512</size>
				</item>
			</weight>
			<convolution>
				<outputNum>128</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>151</name>
			<src>143</src>
			<dst>151</dst>
			<weight>
				<item>
					<dim>1 1 64 128</dim>
					<format>Nhwc</format>
					<offset>1514508</offset>
					<size>32768</size>
				</item>
				<item>
					<dim>128</dim>
					<offset>1547276</offset>
					<size>512</size>
				</item>
			</weight>
			<convolution>
				<outputNum>128</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>152</name>
			<src>149 151</src>
			<dst>152</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>153</name>
			<src>152</src>
			<dst>152</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>155</name>
			<src>152</src>
			<dst>155</dst>
			<weight>
				<item>
					<dim>3 3 128 128</dim>
					<format>Nhwc</format>
					<offset>1547788</offset>
					<size>589824</size>
				</item>
				<item>
					<dim>128</dim>
					<offset>2137612</offset>
					<size>512</size>
				</item>
			</weight>
			<convolution>
				<outputNum>128</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>158</name>
			<src>155</src>
			<dst>158</dst>
			<weight>
				<item>
					<dim>3 3 128 128</dim>
					<format>Nhwc</format>
					<offset>2138124</offset>
					<size>589824</size>
				</item>
				<item>
					<dim>128</dim>
					<offset>2727948</offset>
					<size>512</size>
				</item>
			</weight>
			<convolution>
				<outputNum>128</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>159</name>
			<src>158 152</src>
			<dst>159</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>160</name>
			<src>159</src>
			<dst>159</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>162</name>
			<src>159</src>
			<dst>162</dst>
			<weight>
				<item>
					<dim>3 3 128 256</dim>
					<format>Nhwc</format>
					<offset>2728460</offset>
					<size>1179648</size>
				</item>
				<item>
					<dim>256</dim>
					<offset>3908108</offset>
					<size>1024</size>
				</item>
			</weight>
			<convolution>
				<outputNum>256</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>165</name>
			<src>162</src>
			<dst>165</dst>
			<weight>
				<item>
					<dim>3 3 256 256</dim>
					<format>Nhwc</format>
					<offset>3909132</offset>
					<size>2359296</size>
				</item>
				<item>
					<dim>256</dim>
					<offset>6268428</offset>
					<size>1024</size>
				</item>
			</weight>
			<convolution>
				<outputNum>256</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>167</name>
			<src>159</src>
			<dst>167</dst>
			<weight>
				<item>
					<dim>1 1 128 256</dim>
					<format>Nhwc</format>
					<offset>6269452</offset>
					<size>131072</size>
				</item>
				<item>
					<dim>256</dim>
					<offset>6400524</offset>
					<size>1024</size>
				</item>
			</weight>
			<convolution>
				<outputNum>256</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>168</name>
			<src>165 167</src>
			<dst>168</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>169</name>
			<src>168</src>
			<dst>168</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>171</name>
			<src>168</src>
			<dst>171</dst>
			<weight>
				<item>
					<dim>3 3 256 256</dim>
					<format>Nhwc</format>
					<offset>6401548</offset>
					<size>2359296</size>
				</item>
				<item>
					<dim>256</dim>
					<offset>8760844</offset>
					<size>1024</size>
				</item>
			</weight>
			<convolution>
				<outputNum>256</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>174</name>
			<src>171</src>
			<dst>174</dst>
			<weight>
				<item>
					<dim>3 3 256 256</dim>
					<format>Nhwc</format>
					<offset>8761868</offset>
					<size>2359296</size>
				</item>
				<item>
					<dim>256</dim>
					<offset>11121164</offset>
					<size>1024</size>
				</item>
			</weight>
			<convolution>
				<outputNum>256</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>175</name>
			<src>174 168</src>
			<dst>175</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>176</name>
			<src>175</src>
			<dst>175</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>178</name>
			<src>175</src>
			<dst>178</dst>
			<weight>
				<item>
					<dim>3 3 256 512</dim>
					<format>Nhwc</format>
					<offset>11122188</offset>
					<size>4718592</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>15840780</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>181</name>
			<src>178</src>
			<dst>181</dst>
			<weight>
				<item>
					<dim>3 3 512 512</dim>
					<format>Nhwc</format>
					<offset>15842828</offset>
					<size>9437184</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>25280012</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>183</name>
			<src>175</src>
			<dst>183</dst>
			<weight>
				<item>
					<dim>1 1 256 512</dim>
					<format>Nhwc</format>
					<offset>25282060</offset>
					<size>524288</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>25806348</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>184</name>
			<src>181 183</src>
			<dst>184</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>185</name>
			<src>184</src>
			<dst>184</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>187</name>
			<src>184</src>
			<dst>187</dst>
			<weight>
				<item>
					<dim>3 3 512 512</dim>
					<format>Nhwc</format>
					<offset>25808396</offset>
					<size>9437184</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>35245580</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>190</name>
			<src>187</src>
			<dst>190</dst>
			<weight>
				<item>
					<dim>3 3 512 512</dim>
					<format>Nhwc</format>
					<offset>35247628</offset>
					<size>9437184</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>44684812</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Eltwise</type>
			<name>191</name>
			<src>190 184</src>
			<dst>191</dst>
		</item>
		<item>
			<type>Relu</type>
			<name>192</name>
			<src>191</src>
			<dst>191</dst>
		</item>
		<item>
			<type>Pooling</type>
			<name>193</name>
			<src>191</src>
			<dst>193</dst>
			<pooling>
				<method>Average</method>
				<globalPooling>1</globalPooling>
			</pooling>
		</item>
		<item>
			<type>Reshape</type>
			<name>194</name>
			<src>193</src>
			<dst>194</dst>
			<reshape>
				<shape>512</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>InnerProduct</type>
			<name>196</name>
			<src>194</src>
			<dst>196</dst>
			<weight>
				<item>
					<dim>4 512</dim>
					<format>Nhwc</format>
					<offset>44686892</offset>
					<size>8192</size>
				</item>
				<item>
					<dim>4</dim>
					<offset>44695084</offset>
					<size>16</size>
				</item>
			</weight>
			<innerProduct>
				<outputNum>4</outputNum>
			</innerProduct>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_250</name>
			<src>196</src>
			<dst>Reshape_250</dst>
			<reshape>
				<shape>4</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Softmax</type>
			<name>Softmax_253</name>
			<src>Reshape_250</src>
			<dst>Reshape_250</dst>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_254</name>
			<src>196</src>
			<dst>ShapeOf_254</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>type</name>
			<src>Reshape_250 ShapeOf_254</src>
			<dst>type</dst>
		</item>
		<item>
			<type>InnerProduct</type>
			<name>195</name>
			<src>194</src>
			<dst>195</dst>
			<weight>
				<item>
					<dim>7 512</dim>
					<format>Nhwc</format>
					<offset>44695116</offset>
					<size>14336</size>
				</item>
				<item>
					<dim>7</dim>
					<offset>44709452</offset>
					<size>28</size>
				</item>
			</weight>
			<innerProduct>
				<outputNum>7</outputNum>
			</innerProduct>
		</item>
		<item>
			<type>Reshape</type>
			<name>Reshape_241</name>
			<src>195</src>
			<dst>Reshape_241</dst>
			<reshape>
				<shape>7</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Softmax</type>
			<name>Softmax_244</name>
			<src>Reshape_241</src>
			<dst>Reshape_241</dst>
		</item>
		<item>
			<type>Meta</type>
			<name>ShapeOf_245</name>
			<src>195</src>
			<dst>ShapeOf_245</dst>
			<meta>
				<type>Shape</type>
				<version>1</version>
			</meta>
		</item>
		<item>
			<type>Reshape</type>
			<name>color</name>
			<src>Reshape_241 ShapeOf_245</src>
			<dst>color</dst>
		</item>
		<item>
			<type>Stub</type>
			<name>color/sink_port_0</name>
			<src>color</src>
			<dst>color/sink_port_0</dst>
		</item>
		<item>
			<type>Stub</type>
			<name>type/sink_port_0</name>
			<src>type</src>
			<dst>type/sink_port_0</dst>
		</item>
	</layers>
</network>

