<?xml version="1.0" encoding="utf-8"?>
<network>
	<version>1</version>
	<name>person-vehicle-bike-detection-crossroad-1016</name>
	<layers>
		<item>
			<type>Input</type>
			<name>input.1</name>
			<dst>input.1</dst>
			<input>
				<shape>
					<item>
						<dim>1 512 512 3</dim>
						<format>Nhwc</format>
					</item>
				</shape>
			</input>
		</item>
		<item>
			<type>Fused</type>
			<name>Mul1_8335/Fused_Mul_/FusedScaleShift_</name>
			<src>input.1</src>
			<dst>Mul1_8335/Fused_Mul_/FusedScaleShift_</dst>
			<weight>
				<item>
					<dim>3</dim>
					<offset>0</offset>
					<size>12</size>
				</item>
				<item>
					<dim>3</dim>
					<offset>12</offset>
					<size>12</size>
				</item>
			</weight>
			<fused>
				<type>10</type>
				<floats>0.003922 0.000000 1.000000 0.000000</floats>
			</fused>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>398</name>
			<src>Mul1_8335/Fused_Mul_/FusedScaleShift_</src>
			<dst>398</dst>
			<weight>
				<item>
					<dim>3 3 3 32</dim>
					<format>Nhwc</format>
					<offset>24</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>3480</offset>
					<size>128</size>
				</item>
				<item>
					<dim>3 3 1 32</dim>
					<format>Nhwc</format>
					<offset>3608</offset>
					<size>1152</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>4760</offset>
					<size>128</size>
				</item>
				<item>
					<dim>1 1 32 16</dim>
					<format>Nhwc</format>
					<offset>4888</offset>
					<size>2048</size>
				</item>
				<item>
					<dim>16</dim>
					<offset>6936</offset>
					<size>64</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>32</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>32</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>16</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>406</name>
			<src>398</src>
			<dst>406</dst>
			<weight>
				<item>
					<dim>1 1 16 96</dim>
					<format>Nhwc</format>
					<offset>7000</offset>
					<size>6144</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>13144</offset>
					<size>384</size>
				</item>
				<item>
					<dim>3 3 1 96</dim>
					<format>Nhwc</format>
					<offset>13528</offset>
					<size>3456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>16984</offset>
					<size>384</size>
				</item>
				<item>
					<dim>1 1 96 24</dim>
					<format>Nhwc</format>
					<offset>17368</offset>
					<size>9216</size>
				</item>
				<item>
					<dim>24</dim>
					<offset>26584</offset>
					<size>96</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>96</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>24</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>416</name>
			<src>406</src>
			<dst>416</dst>
			<weight>
				<item>
					<dim>1 1 24 144</dim>
					<format>Nhwc</format>
					<offset>26680</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>40504</offset>
					<size>576</size>
				</item>
				<item>
					<dim>3 3 1 144</dim>
					<format>Nhwc</format>
					<offset>41080</offset>
					<size>5184</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>46264</offset>
					<size>576</size>
				</item>
				<item>
					<dim>1 1 144 24</dim>
					<format>Nhwc</format>
					<offset>46840</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>24</dim>
					<offset>60664</offset>
					<size>96</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>144</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>144</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>144</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>24</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>423</name>
			<src>416</src>
			<dst>423</dst>
			<weight>
				<item>
					<dim>1 1 24 144</dim>
					<format>Nhwc</format>
					<offset>60760</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>74584</offset>
					<size>576</size>
				</item>
				<item>
					<dim>3 3 1 144</dim>
					<format>Nhwc</format>
					<offset>75160</offset>
					<size>5184</size>
				</item>
				<item>
					<dim>144</dim>
					<offset>80344</offset>
					<size>576</size>
				</item>
				<item>
					<dim>1 1 144 32</dim>
					<format>Nhwc</format>
					<offset>80920</offset>
					<size>18432</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>99352</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>144</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>144</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>144</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>433</name>
			<src>423</src>
			<dst>433</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>99480</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>124056</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>124824</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>131736</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 32</dim>
					<format>Nhwc</format>
					<offset>132504</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>157080</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>442</name>
			<src>433</src>
			<dst>442</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>157208</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>181784</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>182552</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>189464</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 32</dim>
					<format>Nhwc</format>
					<offset>190232</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>32</dim>
					<offset>214808</offset>
					<size>128</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>32</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>449</name>
			<src>442</src>
			<dst>449</dst>
			<weight>
				<item>
					<dim>1 1 32 192</dim>
					<format>Nhwc</format>
					<offset>214936</offset>
					<size>24576</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>239512</offset>
					<size>768</size>
				</item>
				<item>
					<dim>3 3 1 192</dim>
					<format>Nhwc</format>
					<offset>240280</offset>
					<size>6912</size>
				</item>
				<item>
					<dim>192</dim>
					<offset>247192</offset>
					<size>768</size>
				</item>
				<item>
					<dim>1 1 192 64</dim>
					<format>Nhwc</format>
					<offset>247960</offset>
					<size>49152</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>297112</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>192</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>192</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>2 2</stride>
						<dilation>1 1</dilation>
						<group>192</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>459</name>
			<src>449</src>
			<dst>459</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>297368</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>395672</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>397208</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>411032</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>412568</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>510872</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>468</name>
			<src>459</src>
			<dst>468</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>511128</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>609432</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>610968</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>624792</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>626328</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>724632</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>477</name>
			<src>468</src>
			<dst>477</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>724888</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>823192</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>824728</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>838552</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 64</dim>
					<format>Nhwc</format>
					<offset>840088</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>64</dim>
					<offset>938392</offset>
					<size>256</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>64</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>484</name>
			<src>477</src>
			<dst>484</dst>
			<weight>
				<item>
					<dim>1 1 64 384</dim>
					<format>Nhwc</format>
					<offset>938648</offset>
					<size>98304</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>1036952</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>3 3 1 384</dim>
					<format>Nhwc</format>
					<offset>1038488</offset>
					<size>13824</size>
				</item>
				<item>
					<dim>384</dim>
					<offset>1052312</offset>
					<size>1536</size>
				</item>
				<item>
					<dim>1 1 384 96</dim>
					<format>Nhwc</format>
					<offset>1053848</offset>
					<size>147456</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>1201304</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>384</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>384</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>384</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>494</name>
			<src>484</src>
			<dst>494</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>1201688</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1422872</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>1425176</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1445912</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 96</dim>
					<format>Nhwc</format>
					<offset>1448216</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>1669400</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>503</name>
			<src>494</src>
			<dst>503</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>1669784</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1890968</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>1893272</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>1914008</offset>
					<size>2304</size>
				</item>
				<item>
					<dim>1 1 576 96</dim>
					<format>Nhwc</format>
					<offset>1916312</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>96</dim>
					<offset>2137496</offset>
					<size>384</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>576</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>576</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>576</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>96</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>504</name>
			<src>503</src>
			<dst>504</dst>
			<weight>
				<item>
					<dim>1 1 96 576</dim>
					<format>Nhwc</format>
					<offset>2137880</offset>
					<size>221184</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>2359064</offset>
					<size>2304</size>
				</item>
			</weight>
			<convolution>
				<outputNum>576</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>554</name>
			<src>504</src>
			<dst>554</dst>
			<weight>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>2361368</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>2382104</offset>
					<size>2304</size>
				</item>
			</weight>
			<convolution>
				<outputNum>576</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<group>576</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>557</name>
			<src>554</src>
			<dst>557</dst>
			<weight>
				<item>
					<dim>1 1 576 36</dim>
					<format>Nhwc</format>
					<offset>2384408</offset>
					<size>82944</size>
				</item>
				<item>
					<dim>36</dim>
					<offset>2467352</offset>
					<size>144</size>
				</item>
			</weight>
			<convolution>
				<outputNum>36</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Permute</type>
			<name>625</name>
			<src>557</src>
			<dst>625</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Reshape</type>
			<name>633</name>
			<src>625</src>
			<dst>633</dst>
			<reshape>
				<shape>-1</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Convolution</type>
			<name>507</name>
			<src>504</src>
			<dst>507</dst>
			<weight>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>2467504</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>2488240</offset>
					<size>2304</size>
				</item>
			</weight>
			<convolution>
				<outputNum>576</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<group>576</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>510</name>
			<src>507</src>
			<dst>510</dst>
			<weight>
				<item>
					<dim>1 1 576 160</dim>
					<format>Nhwc</format>
					<offset>2490544</offset>
					<size>368640</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>2859184</offset>
					<size>640</size>
				</item>
			</weight>
			<convolution>
				<outputNum>160</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>520</name>
			<src>510</src>
			<dst>520</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>2859824</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>3474224</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>3478064</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>3512624</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 160</dim>
					<format>Nhwc</format>
					<offset>3516464</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>4130864</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>529</name>
			<src>520</src>
			<dst>529</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>4131504</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>4745904</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>4749744</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>4784304</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 160</dim>
					<format>Nhwc</format>
					<offset>4788144</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>160</dim>
					<offset>5402544</offset>
					<size>640</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>160</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
				<add>1</add>
			</mergedConvolution>
		</item>
		<item>
			<type>MergedConvolution</type>
			<name>536</name>
			<src>529</src>
			<dst>536</dst>
			<weight>
				<item>
					<dim>1 1 160 960</dim>
					<format>Nhwc</format>
					<offset>5403184</offset>
					<size>614400</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>6017584</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>3 3 1 960</dim>
					<format>Nhwc</format>
					<offset>6021424</offset>
					<size>34560</size>
				</item>
				<item>
					<dim>960</dim>
					<offset>6055984</offset>
					<size>3840</size>
				</item>
				<item>
					<dim>1 1 960 320</dim>
					<format>Nhwc</format>
					<offset>6059824</offset>
					<size>1228800</size>
				</item>
				<item>
					<dim>320</dim>
					<offset>7288624</offset>
					<size>1280</size>
				</item>
			</weight>
			<mergedConvolution>
				<conv>
					<item>
						<outputNum>960</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>960</outputNum>
						<kernel>3 3</kernel>
						<pad>1 1 1 1</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
						<group>960</group>
						<activationType>Relu</activationType>
					</item>
					<item>
						<outputNum>320</outputNum>
						<kernel>1 1</kernel>
						<pad>0 0 0 0</pad>
						<stride>1 1</stride>
						<dilation>1 1</dilation>
					</item>
				</conv>
			</mergedConvolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>538</name>
			<src>536</src>
			<dst>538</dst>
			<weight>
				<item>
					<dim>1 1 320 1280</dim>
					<format>Nhwc</format>
					<offset>7289904</offset>
					<size>1638400</size>
				</item>
				<item>
					<dim>1280</dim>
					<offset>8928304</offset>
					<size>5120</size>
				</item>
			</weight>
			<convolution>
				<outputNum>1280</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>562</name>
			<src>538</src>
			<dst>562</dst>
			<weight>
				<item>
					<dim>3 3 1 1280</dim>
					<format>Nhwc</format>
					<offset>8933424</offset>
					<size>46080</size>
				</item>
				<item>
					<dim>1280</dim>
					<offset>8979504</offset>
					<size>5120</size>
				</item>
			</weight>
			<convolution>
				<outputNum>1280</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<group>1280</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>565</name>
			<src>562</src>
			<dst>565</dst>
			<weight>
				<item>
					<dim>1 1 1280 36</dim>
					<format>Nhwc</format>
					<offset>8984624</offset>
					<size>184320</size>
				</item>
				<item>
					<dim>36</dim>
					<offset>9168944</offset>
					<size>144</size>
				</item>
			</weight>
			<convolution>
				<outputNum>36</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Permute</type>
			<name>634</name>
			<src>565</src>
			<dst>634</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Reshape</type>
			<name>642</name>
			<src>634</src>
			<dst>642</dst>
			<reshape>
				<shape>-1</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Convolution</type>
			<name>541</name>
			<src>538</src>
			<dst>541</dst>
			<weight>
				<item>
					<dim>1 1 1280 256</dim>
					<format>Nhwc</format>
					<offset>9169088</offset>
					<size>1310720</size>
				</item>
				<item>
					<dim>256</dim>
					<offset>10479808</offset>
					<size>1024</size>
				</item>
			</weight>
			<convolution>
				<outputNum>256</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>544</name>
			<src>541</src>
			<dst>544</dst>
			<weight>
				<item>
					<dim>3 3 1 256</dim>
					<format>Nhwc</format>
					<offset>10480832</offset>
					<size>9216</size>
				</item>
				<item>
					<dim>256</dim>
					<offset>10490048</offset>
					<size>1024</size>
				</item>
			</weight>
			<convolution>
				<outputNum>256</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>2 2</stride>
				<dilation>1 1</dilation>
				<group>256</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>547</name>
			<src>544</src>
			<dst>547</dst>
			<weight>
				<item>
					<dim>1 1 256 512</dim>
					<format>Nhwc</format>
					<offset>10491072</offset>
					<size>524288</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>11015360</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>570</name>
			<src>547</src>
			<dst>570</dst>
			<weight>
				<item>
					<dim>3 3 1 512</dim>
					<format>Nhwc</format>
					<offset>11017408</offset>
					<size>18432</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>11035840</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<group>512</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>573</name>
			<src>570</src>
			<dst>573</dst>
			<weight>
				<item>
					<dim>1 1 512 36</dim>
					<format>Nhwc</format>
					<offset>11037888</offset>
					<size>73728</size>
				</item>
				<item>
					<dim>36</dim>
					<offset>11111616</offset>
					<size>144</size>
				</item>
			</weight>
			<convolution>
				<outputNum>36</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Permute</type>
			<name>643</name>
			<src>573</src>
			<dst>643</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Reshape</type>
			<name>651</name>
			<src>643</src>
			<dst>651</dst>
			<reshape>
				<shape>-1</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Concat</type>
			<name>652</name>
			<src>633 642 651</src>
			<dst>652</dst>
		</item>
		<item>
			<type>Convolution</type>
			<name>550</name>
			<src>504</src>
			<dst>550</dst>
			<weight>
				<item>
					<dim>3 3 1 576</dim>
					<format>Nhwc</format>
					<offset>11111760</offset>
					<size>20736</size>
				</item>
				<item>
					<dim>576</dim>
					<offset>11132496</offset>
					<size>2304</size>
				</item>
			</weight>
			<convolution>
				<outputNum>576</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<group>576</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>553</name>
			<src>550</src>
			<dst>553</dst>
			<weight>
				<item>
					<dim>1 1 576 36</dim>
					<format>Nhwc</format>
					<offset>11134800</offset>
					<size>82944</size>
				</item>
				<item>
					<dim>36</dim>
					<offset>11217744</offset>
					<size>144</size>
				</item>
			</weight>
			<convolution>
				<outputNum>36</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Permute</type>
			<name>578</name>
			<src>553</src>
			<dst>578</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Reshape</type>
			<name>586</name>
			<src>578</src>
			<dst>586</dst>
			<reshape>
				<shape>-1</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Convolution</type>
			<name>558</name>
			<src>538</src>
			<dst>558</dst>
			<weight>
				<item>
					<dim>3 3 1 1280</dim>
					<format>Nhwc</format>
					<offset>11217888</offset>
					<size>46080</size>
				</item>
				<item>
					<dim>1280</dim>
					<offset>11263968</offset>
					<size>5120</size>
				</item>
			</weight>
			<convolution>
				<outputNum>1280</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<group>1280</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>561</name>
			<src>558</src>
			<dst>561</dst>
			<weight>
				<item>
					<dim>1 1 1280 36</dim>
					<format>Nhwc</format>
					<offset>11269088</offset>
					<size>184320</size>
				</item>
				<item>
					<dim>36</dim>
					<offset>11453408</offset>
					<size>144</size>
				</item>
			</weight>
			<convolution>
				<outputNum>36</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Permute</type>
			<name>587</name>
			<src>561</src>
			<dst>587</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Reshape</type>
			<name>595</name>
			<src>587</src>
			<dst>595</dst>
			<reshape>
				<shape>-1</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Convolution</type>
			<name>566</name>
			<src>547</src>
			<dst>566</dst>
			<weight>
				<item>
					<dim>3 3 1 512</dim>
					<format>Nhwc</format>
					<offset>11453552</offset>
					<size>18432</size>
				</item>
				<item>
					<dim>512</dim>
					<offset>11471984</offset>
					<size>2048</size>
				</item>
			</weight>
			<convolution>
				<outputNum>512</outputNum>
				<kernel>3 3</kernel>
				<pad>1 1 1 1</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
				<group>512</group>
				<activationType>Relu</activationType>
			</convolution>
		</item>
		<item>
			<type>Convolution</type>
			<name>569</name>
			<src>566</src>
			<dst>569</dst>
			<weight>
				<item>
					<dim>1 1 512 36</dim>
					<format>Nhwc</format>
					<offset>11474032</offset>
					<size>73728</size>
				</item>
				<item>
					<dim>36</dim>
					<offset>11547760</offset>
					<size>144</size>
				</item>
			</weight>
			<convolution>
				<outputNum>36</outputNum>
				<kernel>1 1</kernel>
				<pad>0 0 0 0</pad>
				<stride>1 1</stride>
				<dilation>1 1</dilation>
			</convolution>
		</item>
		<item>
			<type>Permute</type>
			<name>596</name>
			<src>569</src>
			<dst>596</dst>
			<permute>
				<order>0 1 2 3</order>
				<format>Nchw</format>
			</permute>
		</item>
		<item>
			<type>Reshape</type>
			<name>604</name>
			<src>596</src>
			<dst>604</dst>
			<reshape>
				<shape>-1</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Concat</type>
			<name>605</name>
			<src>586 595 604</src>
			<dst>605</dst>
		</item>
		<item>
			<type>Reshape</type>
			<name>615</name>
			<src>605</src>
			<dst>615</dst>
			<reshape>
				<shape>-1 4</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>Softmax</type>
			<name>616</name>
			<src>615</src>
			<dst>616</dst>
			<softmax>
				<axis>2</axis>
			</softmax>
		</item>
		<item>
			<type>Reshape</type>
			<name>624</name>
			<src>616</src>
			<dst>624</dst>
			<reshape>
				<shape>-1</shape>
				<axis>1</axis>
			</reshape>
		</item>
		<item>
			<type>PriorBoxClustered</type>
			<name>574</name>
			<src>504 Mul1_8335/Fused_Mul_/FusedScaleShift_</src>
			<dst>574</dst>
			<priorBoxClustered>
				<widths>17.136999 38.165001 70.690002 9.584000 17.634001 23.743999 6.507000 12.245000 14.749000</widths>
				<heights>20.733000 45.464001 78.592003 29.393000 55.397999 84.879997 17.006001 28.673000 44.110001</heights>
				<variance>0.100000 0.100000 0.200000 0.200000</variance>
				<step>16.000000</step>
			</priorBoxClustered>
		</item>
		<item>
			<type>PriorBoxClustered</type>
			<name>575</name>
			<src>538 Mul1_8335/Fused_Mul_/FusedScaleShift_</src>
			<dst>575</dst>
			<priorBoxClustered>
				<widths>81.752998 153.182999 169.567001 32.147999 41.048000 52.198002 32.390999 22.396999 33.216000</widths>
				<heights>157.378998 104.697998 210.544998 118.319000 157.328003 203.363007 36.256001 64.450996 101.718002</heights>
				<variance>0.100000 0.100000 0.200000 0.200000</variance>
				<step>32.000000</step>
			</priorBoxClustered>
		</item>
		<item>
			<type>PriorBoxClustered</type>
			<name>576</name>
			<src>547 Mul1_8335/Fused_Mul_/FusedScaleShift_</src>
			<dst>576</dst>
			<priorBoxClustered>
				<widths>110.651001 237.237000 348.269012 65.598000 82.728996 110.538002 53.240002 68.246002 105.444000</widths>
				<heights>344.063995 243.970993 337.748993 256.941010 327.187012 428.114014 68.918999 155.867004 270.048004</heights>
				<variance>0.100000 0.100000 0.200000 0.200000</variance>
				<step>64.000000</step>
			</priorBoxClustered>
		</item>
		<item>
			<type>Concat</type>
			<name>577</name>
			<src>574 575 576</src>
			<dst>577</dst>
			<concat>
				<axis>2</axis>
			</concat>
		</item>
		<item>
			<type>DetectionOutput</type>
			<name>653</name>
			<src>652 624 577</src>
			<dst>653</dst>
			<detectionOutput>
				<numClasses>4</numClasses>
				<nms>
					<nmsThreshold>0.450000</nmsThreshold>
					<topK>200</topK>
				</nms>
				<codeType>CenterSize</codeType>
				<keepTopK>200</keepTopK>
				<confidenceThreshold>0.020000</confidenceThreshold>
			</detectionOutput>
		</item>
	</layers>
</network>

